import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import {BrowserRouter , Routes , Route} from 'react-router-dom'

import Login from './pages/Login'
import Register from './pages/Register'
import TicTacToe from './pages/TicTacToe'
import MultiplayerTicTacToe from './pages/MultiplayerTicTacToe'
import Home from './pages/Home'


createRoot(document.getElementById('root')).render(
  <BrowserRouter>
    <Routes>
      <Route path='/login' element={<Login/>}></Route>
      <Route path='/register' element={<Register/>}></Route>
      <Route path='/tictactoe' element={<TicTacToe/>}></Route>
      <Route path='/multiplayer' element={<MultiplayerTicTacToe/>}></Route>
      <Route path='/' element={<Home/>}></Route>
    </Routes>
  </BrowserRouter>
)
