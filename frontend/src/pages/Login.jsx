import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (!password || !email) {
        setError("กรุณากรอกข้อมูลให้ครบ");
        return;
      }
      if (password.length < 8) {
        setError("กรุณากรอกรหัสผ่านมากกว่า 8 ตัว");
        return;
      }
      await axios.post("http://localhost:5000/login", {
        email,
        password,
      });
      setError("");
      alert("เข้าสู่ระบบสำเร็จ");
      navigate("/game");
    } catch (err) {
      setError(err.response?.data?.error || "เกิดข้อผิดพลาด");
    }
  };

  return (
    <>
      <h1>Login</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="email"
          value={email}
          id="email"
          placeholder="please enter email"
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          type="password"
          value={password}
          id="password"
          placeholder="please enter password"
          onChange={(e) => setPassword(e.target.value)}
        />
        <button type="submit">เข้าสู่ระบบ</button>
      </form>
      {error && <p style={{ color: "red" }}> {error}</p>}
      <p>
        ถ้ายังไม่มีบัญชี <Link to="/Register">สมัครสมาชิก</Link>
      </p>
    </>
  );
}

export default Login;
