import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";

function Register() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [passwordConfirm, setPasswordConfirm] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (!password || !email) {
        setError("กรุณากรอกข้อมูลให้ครบ");
        return;
      }
      if (password.length < 8) {
        setError("กรุณากรอกรหัสผ่านมากกว่า 8 ตัว");
        return;
      }
      if (password !== passwordConfirm) {
        setError("รหัสผ่านไม่ตรงกัน");
        return;
      }
      await axios.post("http://localhost:5000/register", {
        email,
        password,
      });
      setError("");
      alert("สมัครบัญชีสำเร็จ");
      navigate("/Login");
    } catch (err) {
      setError(err.response?.data?.error || "เกิดข้อผิดพลาด");
    }
  };

  return (
    <>
      <h1>Register page</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={email}
          id="email"
          placeholder="please enter email"
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          type="password"
          value={password}
          id="password"
          placeholder="please enter password"
          onChange={(e) => setPassword(e.target.value)}
        />
        <input
          type="password"
          value={passwordConfirm}
          id="passwordConfirm"
          placeholder="please enter password confirm"
          onChange={(e) => setPasswordConfirm(e.target.value)}
        />
        <button type="submit">สมัครสมาชิก</button>
      </form>
      {error && <p style={{ color: "red" }}>{error}</p>}
      <p>
        ถ้าสมัครบัญชีแล้ว <Link to="/Login">เข้าสู่ระบบได้ที่นี่</Link>
      </p>
    </>
  );
}

export default Register;
