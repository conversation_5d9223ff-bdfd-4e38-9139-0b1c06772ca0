.multiplayer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.join-room-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  border: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.join-room-card h1 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  color: white;
  font-weight: bold;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.connected {
  background: #4CAF50;
}

.status-indicator.disconnected {
  background: #f44336;
}

.status-indicator.joined {
  background: #2196F3;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.join-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.input-group {
  text-align: left;
}

.input-group label {
  display: block;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
}

.input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.input-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-group input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.room-input-container {
  display: flex;
  gap: 10px;
}

.room-input-container input {
  flex: 1;
}

.generate-btn {
  padding: 12px 20px;
  background: rgba(76, 175, 80, 0.8);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.generate-btn:hover {
  background: rgba(76, 175, 80, 1);
  transform: translateY(-2px);
}

.join-btn {
  padding: 15px 30px;
  background: rgba(33, 150, 243, 0.8);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.join-btn:hover:not(:disabled) {
  background: rgba(33, 150, 243, 1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.join-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.instructions {
  text-align: left;
  color: white;
}

.instructions h3 {
  margin-bottom: 15px;
  color: rgba(255, 255, 255, 0.9);
}

.instructions ul {
  list-style: none;
  padding: 0;
}

.instructions li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
}

.instructions li::before {
  content: "•";
  color: #4CAF50;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.room-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.players-info {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  justify-content: center;
}

.player-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 25px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.player-card.me {
  border-color: rgba(76, 175, 80, 0.5);
  background: rgba(76, 175, 80, 0.1);
}

.player-card.active {
  border-color: rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.1);
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
  }
}

.player-symbol {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.player-name {
  color: white;
  font-weight: bold;
}

.square.clickable {
  cursor: pointer;
  border: 2px solid rgba(76, 175, 80, 0.5);
}

.square.clickable:hover {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
  .join-room-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .join-room-card h1 {
    font-size: 2rem;
  }
  
  .players-info {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .room-input-container {
    flex-direction: column;
  }
  
  .generate-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .join-room-card h1 {
    font-size: 1.8rem;
  }
  
  .input-group input,
  .generate-btn,
  .join-btn {
    font-size: 0.9rem;
  }
}
