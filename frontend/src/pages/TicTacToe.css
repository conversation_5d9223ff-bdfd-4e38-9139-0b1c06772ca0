.tictactoe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.game-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 0;
  left: 0;
  color: white;
  text-decoration: none;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-weight: bold;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.game-header h1 {
  color: white;
  font-size: 3rem;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.scoreboard {
  display: flex;
  gap: 30px;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 30px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.score-item {
  text-align: center;
}

.player-x {
  color: #ff6b6b;
  font-weight: bold;
  font-size: 1.2rem;
}

.player-o {
  color: #4ecdc4;
  font-weight: bold;
  font-size: 1.2rem;
}

.draws {
  color: #ffd93d;
  font-weight: bold;
  font-size: 1.2rem;
}

.game-board {
  margin-bottom: 30px;
}

.board {
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  gap: 5px;
  background: #2c3e50;
  padding: 10px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.board-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
}

.square {
  width: 100px;
  height: 100px;
  background: white;
  border: none;
  border-radius: 10px;
  font-size: 2.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.square:hover:not(:disabled) {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.square:active:not(:disabled) {
  transform: translateY(0);
}

.square:disabled {
  cursor: not-allowed;
}

.square.filled {
  animation: popIn 0.3s ease;
}

.square:contains("X") {
  color: #ff6b6b;
}

.square:contains("O") {
  color: #4ecdc4;
}

@keyframes popIn {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.game-info {
  text-align: center;
}

.status {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 20px;
  padding: 15px 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status.winner {
  background: rgba(76, 175, 80, 0.3);
  animation: celebrate 0.6s ease;
}

@keyframes celebrate {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.game-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: #4CAF50;
  color: white;
}

.btn-primary:hover {
  background: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background: #f44336;
  color: white;
}

.btn-secondary:hover {
  background: #da190b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .game-header h1 {
    font-size: 2rem;
  }
  
  .scoreboard {
    flex-direction: column;
    gap: 10px;
  }
  
  .square {
    width: 80px;
    height: 80px;
    font-size: 2rem;
  }
  
  .game-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .square {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }
  
  .game-header h1 {
    font-size: 1.5rem;
  }
}
