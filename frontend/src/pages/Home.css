.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.home-content {
  text-align: center;
  max-width: 800px;
  width: 100%;
}

.home-title {
  font-size: 4rem;
  color: white;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease;
}

.home-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 50px;
  animation: fadeInUp 1s ease 0.2s both;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
  animation: fadeInUp 1s ease 0.4s both;
}

.game-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px 20px;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

.game-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.game-card.coming-soon {
  opacity: 0.6;
  cursor: not-allowed;
}

.game-card.coming-soon:hover {
  transform: none;
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}

.game-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  display: block;
}

.game-card h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  font-weight: bold;
}

.game-card p {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
}

.auth-links {
  display: flex;
  gap: 20px;
  justify-content: center;
  animation: fadeInUp 1s ease 0.6s both;
}

.auth-link {
  padding: 12px 30px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border-radius: 25px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  font-weight: bold;
  backdrop-filter: blur(10px);
}

.auth-link:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .home-title {
    font-size: 3rem;
  }
  
  .games-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .auth-links {
    flex-direction: column;
    align-items: center;
  }
  
  .auth-link {
    width: 200px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .home-title {
    font-size: 2.5rem;
  }
  
  .home-subtitle {
    font-size: 1rem;
  }
  
  .game-card {
    padding: 30px 15px;
  }
  
  .game-icon {
    font-size: 3rem;
  }
}
