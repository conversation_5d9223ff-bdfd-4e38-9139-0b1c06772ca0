import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import io from 'socket.io-client';
import './TicTacToe.css';
import './MultiplayerTicTacToe.css';

const MultiplayerTicTacToe = () => {
  const [socket, setSocket] = useState(null);
  const [roomId, setRoomId] = useState('');
  const [playerName, setPlayerName] = useState('');
  const [gameState, setGameState] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  useEffect(() => {
    // Initialize socket connection
    // สำหรับ ngrok ให้แทนที่ด้วย ngrok URL ของ backend
    // เช่น: 'https://abc123.ngrok.io'
    const backendUrl = window.location.hostname === 'localhost'
      ? 'http://localhost:5000'
      : `http://${window.location.hostname}:5000`;
    const newSocket = io(backendUrl);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setIsConnected(true);
      setConnectionStatus('connected');
    });

    newSocket.on('disconnect', () => {
      setIsConnected(false);
      setConnectionStatus('disconnected');
    });

    newSocket.on('room-update', (roomData) => {
      setGameState(roomData);
    });

    newSocket.on('room-full', () => {
      alert('ห้องเต็มแล้ว! กรุณาลองห้องอื่น');
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const joinRoom = () => {
    if (!roomId.trim() || !playerName.trim()) {
      alert('กรุณากรอกชื่อผู้เล่นและรหัสห้อง');
      return;
    }
    
    if (socket) {
      socket.emit('join-room', roomId.trim(), playerName.trim());
      setConnectionStatus('joined');
    }
  };

  const makeMove = (position) => {
    if (socket && gameState) {
      socket.emit('make-move', roomId, position);
    }
  };

  const resetGame = () => {
    if (socket) {
      socket.emit('reset-game', roomId);
    }
  };

  const generateRoomId = () => {
    const id = Math.random().toString(36).substring(2, 8).toUpperCase();
    setRoomId(id);
  };

  const renderSquare = (index) => {
    const isMyTurn = gameState && 
      gameState.players.find(p => p.id === socket?.id)?.symbol === gameState.currentPlayer;
    
    return (
      <button
        className={`square ${gameState?.board[index] ? 'filled' : ''} ${
          isMyTurn && !gameState?.board[index] && gameState?.gameStatus === 'playing' ? 'clickable' : ''
        }`}
        onClick={() => makeMove(index)}
        disabled={
          !gameState || 
          gameState.board[index] || 
          gameState.gameStatus !== 'playing' || 
          !isMyTurn
        }
      >
        {gameState?.board[index]}
      </button>
    );
  };

  const getGameStatus = () => {
    if (!gameState) return '';
    
    const myPlayer = gameState.players.find(p => p.id === socket?.id);
    
    switch (gameState.gameStatus) {
      case 'waiting':
        return `รอผู้เล่นคนที่ 2... (${gameState.players.length}/2)`;
      case 'playing':
        const currentPlayerName = gameState.players.find(p => p.symbol === gameState.currentPlayer)?.name;
        const isMyTurn = myPlayer?.symbol === gameState.currentPlayer;
        return isMyTurn ? 'ตาของคุณ!' : `ตาของ ${currentPlayerName}`;
      case 'finished':
        const winnerName = gameState.players.find(p => p.symbol === gameState.winner)?.name;
        return myPlayer?.symbol === gameState.winner ? 'คุณชนะ! 🎉' : `${winnerName} ชนะ!`;
      case 'draw':
        return 'เสมอ!';
      default:
        return '';
    }
  };

  // If not connected to a room yet
  if (!gameState) {
    return (
      <div className="multiplayer-container">
        <div className="join-room-card">
          <Link to="/" className="back-button">← กลับหน้าหลัก</Link>
          <h1>🎮 Multiplayer Tic-Tac-Toe</h1>
          
          <div className="connection-status">
            <span className={`status-indicator ${connectionStatus}`}></span>
            {connectionStatus === 'connected' ? 'เชื่อมต่อแล้ว' : 'กำลังเชื่อมต่อ...'}
          </div>

          <div className="join-form">
            <div className="input-group">
              <label>ชื่อผู้เล่น:</label>
              <input
                type="text"
                value={playerName}
                onChange={(e) => setPlayerName(e.target.value)}
                placeholder="กรอกชื่อของคุณ"
                maxLength={20}
              />
            </div>

            <div className="input-group">
              <label>รหัสห้อง:</label>
              <div className="room-input-container">
                <input
                  type="text"
                  value={roomId}
                  onChange={(e) => setRoomId(e.target.value.toUpperCase())}
                  placeholder="กรอกรหัสห้อง"
                  maxLength={6}
                />
                <button 
                  className="generate-btn"
                  onClick={generateRoomId}
                  type="button"
                >
                  สุ่ม
                </button>
              </div>
            </div>

            <button 
              className="join-btn"
              onClick={joinRoom}
              disabled={!isConnected || !roomId.trim() || !playerName.trim()}
            >
              เข้าร่วมห้อง
            </button>
          </div>

          <div className="instructions">
            <h3>วิธีเล่น:</h3>
            <ul>
              <li>กรอกชื่อผู้เล่นและรหัสห้อง</li>
              <li>แชร์รหัสห้องให้เพื่อน</li>
              <li>รอให้เพื่อนเข้าร่วมห้อง</li>
              <li>เริ่มเล่นกันได้เลย!</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  // Game interface
  return (
    <div className="multiplayer-container">
      <div className="game-header">
        <Link to="/" className="back-button">← กลับหน้าหลัก</Link>
        <h1>🎮 Multiplayer Tic-Tac-Toe</h1>
        <div className="room-info">
          <span>ห้อง: {roomId}</span>
        </div>
      </div>

      <div className="players-info">
        {gameState.players.map((player) => (
          <div 
            key={player.id} 
            className={`player-card ${player.id === socket?.id ? 'me' : ''} ${
              gameState.currentPlayer === player.symbol ? 'active' : ''
            }`}
          >
            <span className="player-symbol">{player.symbol}</span>
            <span className="player-name">
              {player.name} {player.id === socket?.id ? '(คุณ)' : ''}
            </span>
          </div>
        ))}
      </div>

      <div className="game-board">
        <div className="board">
          <div className="board-row">
            {renderSquare(0)}
            {renderSquare(1)}
            {renderSquare(2)}
          </div>
          <div className="board-row">
            {renderSquare(3)}
            {renderSquare(4)}
            {renderSquare(5)}
          </div>
          <div className="board-row">
            {renderSquare(6)}
            {renderSquare(7)}
            {renderSquare(8)}
          </div>
        </div>
      </div>

      <div className="game-info">
        <div className={`status ${gameState.gameStatus === 'finished' ? 'winner' : ''}`}>
          {getGameStatus()}
        </div>
        
        {(gameState.gameStatus === 'finished' || gameState.gameStatus === 'draw') && (
          <button className="btn btn-primary" onClick={resetGame}>
            เล่นอีกครั้ง
          </button>
        )}
      </div>
    </div>
  );
};

export default MultiplayerTicTacToe;
