import { useState } from 'react';
import { Link } from 'react-router-dom';
import './TicTacToe.css';

const TicTacToe = () => {
  const [board, setBoard] = useState(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState(true);
  const [gameStatus, setGameStatus] = useState('');
  const [scores, setScores] = useState({ X: 0, O: 0, draws: 0 });

  // ตรวจสอบผู้ชนะ
  const calculateWinner = (squares) => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8], // แถว
      [0, 3, 6], [1, 4, 7], [2, 5, 8], // คอลัมน์
      [0, 4, 8], [2, 4, 6] // เส้นทแยง
    ];

    for (let i = 0; i < lines.length; i++) {
      const [a, b, c] = lines[i];
      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }
    return null;
  };

  // จัดการการคลิกช่อง
  const handleClick = (index) => {
    if (board[index] || calculateWinner(board)) {
      return;
    }

    const newBoard = [...board];
    newBoard[index] = isXNext ? 'X' : 'O';
    setBoard(newBoard);

    const winner = calculateWinner(newBoard);
    if (winner) {
      setGameStatus(`ผู้ชนะคือ ${winner}!`);
      setScores(prev => ({
        ...prev,
        [winner]: prev[winner] + 1
      }));
    } else if (newBoard.every(square => square !== null)) {
      setGameStatus('เสมอ!');
      setScores(prev => ({
        ...prev,
        draws: prev.draws + 1
      }));
    } else {
      setGameStatus(`ตาของ ${isXNext ? 'O' : 'X'}`);
    }

    setIsXNext(!isXNext);
  };

  // เริ่มเกมใหม่
  const resetGame = () => {
    setBoard(Array(9).fill(null));
    setIsXNext(true);
    setGameStatus('ตาของ X');
  };

  // รีเซ็ตคะแนน
  const resetScores = () => {
    setScores({ X: 0, O: 0, draws: 0 });
  };

  // สร้างช่องในกระดาน
  const renderSquare = (index) => {
    return (
      <button
        className={`square ${board[index] ? 'filled' : ''}`}
        onClick={() => handleClick(index)}
        disabled={board[index] || calculateWinner(board)}
      >
        {board[index]}
      </button>
    );
  };

  // กำหนดสถานะเริ่มต้น
  if (!gameStatus) {
    setGameStatus('ตาของ X');
  }

  return (
    <div className="tictactoe-container">
      <div className="game-header">
        <Link to="/" className="back-button">← กลับหน้าหลัก</Link>
        <h1>🎮 Tic-Tac-Toe</h1>
        <div className="scoreboard">
          <div className="score-item">
            <span className="player-x">X: {scores.X}</span>
          </div>
          <div className="score-item">
            <span className="draws">เสมอ: {scores.draws}</span>
          </div>
          <div className="score-item">
            <span className="player-o">O: {scores.O}</span>
          </div>
        </div>
      </div>

      <div className="game-board">
        <div className="board">
          <div className="board-row">
            {renderSquare(0)}
            {renderSquare(1)}
            {renderSquare(2)}
          </div>
          <div className="board-row">
            {renderSquare(3)}
            {renderSquare(4)}
            {renderSquare(5)}
          </div>
          <div className="board-row">
            {renderSquare(6)}
            {renderSquare(7)}
            {renderSquare(8)}
          </div>
        </div>
      </div>

      <div className="game-info">
        <div className={`status ${calculateWinner(board) ? 'winner' : ''}`}>
          {gameStatus}
        </div>
        <div className="game-controls">
          <button className="btn btn-primary" onClick={resetGame}>
            เกมใหม่
          </button>
          <button className="btn btn-secondary" onClick={resetScores}>
            รีเซ็ตคะแนน
          </button>
        </div>
      </div>
    </div>
  );
};

export default TicTacToe;
