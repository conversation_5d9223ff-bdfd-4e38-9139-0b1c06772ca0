const express = require("express")
const mongoose = require("mongoose")
const cors = require("cors")
const authRoute = require("./routes/auth")


require('dotenv').config()
const app = express()

app.use(cors())
app.use(express.json())
app.use("/" , authRoute)

mongoose.connect(process.env.MONGO_URL) 
    .then(() => {
        console.log("Connected to MongoDB!!!")
        app.listen(5000 , () => console.log("Server running on http://localhost:5000"))
    })
    .catch((error) => console.log("MongoDB Error:" , error))