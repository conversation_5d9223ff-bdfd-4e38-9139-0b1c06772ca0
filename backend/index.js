const express = require("express")
const mongoose = require("mongoose")
const cors = require("cors")
const { createServer } = require("http")
const { Server } = require("socket.io")
const authRoute = require("./routes/auth")

require('dotenv').config()
const app = express()
const httpServer = createServer(app)
const io = new Server(httpServer, {
    cors: {
        origin: [
            "http://localhost:5173",
            "http://localhost:5174",
            "http://*************:5173",
            "http://*************:5174"
        ],
        methods: ["GET", "POST"]
    }
})

app.use(cors())
app.use(express.json())
app.use("/" , authRoute)

// Game state management
const gameRooms = new Map()

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('User connected:', socket.id)

    // Join a game room
    socket.on('join-room', (roomId, playerName) => {
        socket.join(roomId)
        socket.playerName = playerName

        if (!gameRooms.has(roomId)) {
            gameRooms.set(roomId, {
                players: [],
                board: Array(9).fill(null),
                currentPlayer: 'X',
                gameStatus: 'waiting',
                winner: null
            })
        }

        const room = gameRooms.get(roomId)

        // Add player if room is not full
        if (room.players.length < 2) {
            const playerSymbol = room.players.length === 0 ? 'X' : 'O'
            room.players.push({
                id: socket.id,
                name: playerName,
                symbol: playerSymbol
            })

            socket.playerSymbol = playerSymbol
            socket.roomId = roomId

            // Start game if 2 players joined
            if (room.players.length === 2) {
                room.gameStatus = 'playing'
            }

            // Send updated room state to all players in room
            io.to(roomId).emit('room-update', room)
            console.log(`Player ${playerName} (${playerSymbol}) joined room ${roomId}`)
        } else {
            socket.emit('room-full')
        }
    })

    // Handle player move
    socket.on('make-move', (roomId, position) => {
        const room = gameRooms.get(roomId)
        if (!room || room.gameStatus !== 'playing') return

        // Check if it's player's turn
        const player = room.players.find(p => p.id === socket.id)
        if (!player || player.symbol !== room.currentPlayer) return

        // Check if position is valid
        if (room.board[position] !== null) return

        // Make the move
        room.board[position] = player.symbol

        // Check for winner
        const winner = checkWinner(room.board)
        if (winner) {
            room.winner = winner
            room.gameStatus = 'finished'
        } else if (room.board.every(cell => cell !== null)) {
            room.gameStatus = 'draw'
        } else {
            // Switch turns
            room.currentPlayer = room.currentPlayer === 'X' ? 'O' : 'X'
        }

        // Send updated room state
        io.to(roomId).emit('room-update', room)
    })

    // Reset game
    socket.on('reset-game', (roomId) => {
        const room = gameRooms.get(roomId)
        if (!room) return

        room.board = Array(9).fill(null)
        room.currentPlayer = 'X'
        room.gameStatus = 'playing'
        room.winner = null

        io.to(roomId).emit('room-update', room)
    })

    // Handle disconnect
    socket.on('disconnect', () => {
        console.log('User disconnected:', socket.id)

        if (socket.roomId) {
            const room = gameRooms.get(socket.roomId)
            if (room) {
                room.players = room.players.filter(p => p.id !== socket.id)

                if (room.players.length === 0) {
                    gameRooms.delete(socket.roomId)
                } else {
                    room.gameStatus = 'waiting'
                    io.to(socket.roomId).emit('room-update', room)
                }
            }
        }
    })
})

// Helper function to check winner
function checkWinner(board) {
    const lines = [
        [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
        [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
        [0, 4, 8], [2, 4, 6] // diagonals
    ]

    for (let i = 0; i < lines.length; i++) {
        const [a, b, c] = lines[i]
        if (board[a] && board[a] === board[b] && board[a] === board[c]) {
            return board[a]
        }
    }
    return null
}

mongoose.connect(process.env.MONGO_URL)
    .then(() => {
        console.log("Connected to MongoDB!!!")
        httpServer.listen(5000, '0.0.0.0', () => {
            console.log("Server running on:")
            console.log("- Local: http://localhost:5000")
            console.log("- Network: http://*************:5000")
        })
    })
    .catch((error) => console.log("MongoDB Error:" , error))