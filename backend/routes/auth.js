const express = require("express")
const User = require("../models/User")
const router = express.Router();
const bcrypt = require("bcrypt")

router.post("/register" , async (req,res) =>{
    try {
    const {email , password} = req.body
    const exit = await User.findOne({email})
    if (exit)
        return res.status(400).json({message: "อีเมลซ้ำ"})
    const hash = await bcrypt.hash(password , 10)
    const user = new User({email ,password:hash})
    await user.save()
    res.status(201).json({message:"ลงทะเบียนสำเร็จ"})
    }catch(error) {
        res.status(500).json({error: error.message})
    }
})

router.post("/Login" , async (req, res) => {
    try{
        const {email , password} =req.body
        const user = await User.findOne({email})
        if(!user)
            return res.status(400).json({message: "ไม่พบอีเมลของคุณ"})
        const matchPassword = bcrypt.compare(password , user.password)
        if(!matchPassword) 
            return res.status(400).json({message: "รหัสไม่ถูกต้อง"})
        res.status(200).json({message: "เข้าสู่ระบบ"})
    }

    catch(error){
        res.status(500).json({error: error.message})        
    }
})


module.exports = router