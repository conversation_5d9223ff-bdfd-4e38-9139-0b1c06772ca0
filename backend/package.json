{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.16.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}}